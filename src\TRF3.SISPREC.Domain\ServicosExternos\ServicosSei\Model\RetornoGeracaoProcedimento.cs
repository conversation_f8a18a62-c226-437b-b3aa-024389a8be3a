using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.ServicosExternos.ServicosSei.Model
{
    [ExcludeFromCodeCoverage]
    public sealed class RetornoGeracaoProcedimento
    {
        public string? IdProcedimento { get; set; }
        public string? ProcedimentoFormatado { get; set; }
        public string? LinkAcesso { get; set; }
        public RetornoInclusaoDocumento[]? RetornoInclusaoDocumentos { get; set; }
    }

    [ExcludeFromCodeCoverage]
    public sealed class RetornoInclusaoDocumento
    {
        public string? IdDocumento { get; set; }
        public string? DocumentoFormatado { get; set; }
        public string? LinkAcesso { get; set; }
    }
}
