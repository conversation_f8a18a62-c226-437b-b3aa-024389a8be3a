@page
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.Localization
@using TRF3.SISPREC.Web.Menus
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.ExpedirOficios.IndexModel
@{
    PageLayout.Content.Title = "Expedir Ofício - Envio de E-mail";
}

@section scripts {
    <abp-script src="/Pages/ExpedirOficios/index.js"></abp-script>
}

@section styles {
    <abp-style src="/Pages/ExpedirOficios/index.css"></abp-style>
}

<abp-card>
    <abp-card-body>

        <div id="mensagens-tempdata"
             data-mensagem-sucesso="@TempData["MensagemSucesso"]"
             data-mensagem-erro="@TempData["MensagemErro"]"
             hidden>
        </div>

        <form asp-page-handler="EnviarEmail" method="post" id="formEnvioEmail">
            <abp-row>
                <abp-column>
                    <abp-input asp-for="ViewModel.Remetente" size="Small"></abp-input>
                </abp-column>
            </abp-row>

            <abp-row class="d-flex justify-content-end">
                <abp-column class="col-auto">
                    <abp-input asp-for="ViewModel.EnviarCopia" class="align-content-end"></abp-input>
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column>
                    <abp-input asp-for="ViewModel.Destinatarios" size="Small"></abp-input>
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column>
                    <abp-input asp-for="ViewModel.Assunto" size="Small"></abp-input>
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column>
                    <abp-input asp-for="ViewModel.Mensagem" size="Large" class="mensagem-altura-campo"></abp-input>
                </abp-column>
            </abp-row>

            <abp-row class="d-flex justify-content-end">
                <abp-column class="col-auto">
                    <abp-button type="submit" class="btn btn-primary">Enviar</abp-button>
                </abp-column>
            </abp-row>
        </form>
    </abp-card-body>
</abp-card>