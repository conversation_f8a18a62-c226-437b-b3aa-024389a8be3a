using TRF3.SISPREC.ExpedirOficios.Dtos;
using TRF3.SISPREC.ServicosExternos.ServicosSei;
using TRF3.SISPREC.ServicosExternos.ServicosSei.Model;
using Volo.Abp;

namespace TRF3.SISPREC.ExpedirOficios
{
    public class ExpedirOficioAppService : BaseAppService, IExpedirOficioAppService
    {
        private IServicoSei _servicoSei { get; set; }
        private const string MensagemBase = "Falha na comunicação com o sistema SEI.";

        public ExpedirOficioAppService(IServicoSei servicoSei)
        {
            _servicoSei = servicoSei;
        }

        public async Task<RetornoEnvioEmail> EnviarEmail(ExpedirOficioDto expedirOficioDto)
        {
            try
            {
                string enviarComCopia = string.Empty;

                if (expedirOficioDto.EnviarCopia)
                {
                    enviarComCopia = expedirOficioDto.Destinatarios?.Trim() ?? string.Empty;
                    expedirOficioDto.Destinatarios = string.Empty;
                }

                var result = await _servicoSei.EnviarEmail(
                expedirOficioDto.Remetente,
                expedirOficioDto.Destinatarios,
                enviarComCopia,
                expedirOficioDto.Assunto,
                expedirOficioDto.Mensagem,
                null,
                string.Empty);

                return result;
            }
            catch (Exception ex)
            {
                var mensagemFinal = string.IsNullOrWhiteSpace(ex?.Message)
                    ? MensagemBase
                    : $"{MensagemBase} Mensagem: {ex.Message}";

                throw new UserFriendlyException(mensagemFinal, null, null, ex);
            }
        }
    }
}
