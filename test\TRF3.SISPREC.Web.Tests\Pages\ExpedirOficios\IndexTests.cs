using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.ExpedirOficios.Dtos;
using TRF3.SISPREC.ExpedirOficios;
using TRF3.SISPREC.Web.Pages.ExpedirOficios.ViewModels;
using TRF3.SISPREC.Web.Pages.ExpedirOficios;
using Volo.Abp.ObjectMapping;

namespace TRF3.SISPREC.Pages.Tests.ExpedirOficios
{
    public class IndexTests : SISPRECWebTestBase
    {
        private readonly IExpedirOficioAppService _appServiceMock;

        public IndexTests()
        {
            _appServiceMock = Substitute.For<IExpedirOficioAppService>();
        }

        [Fact]
        public async Task OnPostEnviarEmailAsync_Deve_Enviar_Email_E_Definir_Mensagem_Sucesso()
        {
            // Arrange
            var appServiceMock = Substitute.For<IExpedirOficioAppService>();
            var objectMapperMock = Substitute.For<IObjectMapper>();

            var viewModel = new ExpedirOficioViewModel
            {
                Remetente = "<EMAIL>",
                Destinatarios = "<EMAIL>",
                Assunto = "Assunto",
                Mensagem = "Mensagem"
            };

            var dto = new ExpedirOficioDto
            {
                Remetente = viewModel.Remetente,
                Destinatarios = viewModel.Destinatarios,
                Assunto = viewModel.Assunto,
                Mensagem = viewModel.Mensagem
            };

            objectMapperMock
                .Map<ExpedirOficioViewModel, ExpedirOficioDto>(Arg.Is(viewModel))
                .Returns(dto);

            var model = new IndexModel(appServiceMock, objectMapperMock)
            {
                ViewModel = viewModel,
                TempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>())
            };

            // Act
            var result = await model.OnPostEnviarEmailAsync();

            // Assert
            await appServiceMock.Received(1).EnviarEmail(dto);
            model.TempData["MensagemSucesso"].ShouldBe("E-mail enviado com sucesso!");
            result.ShouldBeOfType<RedirectToPageResult>();
        }

        [Fact]
        public async Task OnPostEnviarEmailAsync_Deve_Definir_Mensagem_Erro_Quando_EnviarEmail_LancaException()
        {
            var appServiceMock = Substitute.For<IExpedirOficioAppService>();
            var objectMapperMock = Substitute.For<IObjectMapper>();

            var viewModel = new ExpedirOficioViewModel
            {
                Remetente = "<EMAIL>",
                Destinatarios = "<EMAIL>",
                Assunto = "Assunto",
                Mensagem = "Mensagem"
            };

            var dto = new ExpedirOficioDto
            {
                Remetente = viewModel.Remetente,
                Destinatarios = viewModel.Destinatarios,
                Assunto = viewModel.Assunto,
                Mensagem = viewModel.Mensagem
            };

            var mensagemErroOriginal = "Erro ao comunicar com SEI.";
            var mensagemEsperada = $"Falha na comunicação com o sistema SEI. Mensagem: {mensagemErroOriginal}";

            objectMapperMock
                .Map<ExpedirOficioViewModel, ExpedirOficioDto>(Arg.Is(viewModel))
                .Returns(dto);

            appServiceMock
                .When(x => x.EnviarEmail(Arg.Is(dto)))
                .Do(x => throw new Exception(mensagemEsperada));

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());

            var model = new IndexModel(appServiceMock, objectMapperMock)
            {
                ViewModel = viewModel,
                TempData = tempData
            };

            var result = await model.OnPostEnviarEmailAsync();

            model.TempData["MensagemErro"].ShouldBe(mensagemEsperada);
            result.ShouldBeOfType<RedirectToPageResult>();
        }
    }
}
