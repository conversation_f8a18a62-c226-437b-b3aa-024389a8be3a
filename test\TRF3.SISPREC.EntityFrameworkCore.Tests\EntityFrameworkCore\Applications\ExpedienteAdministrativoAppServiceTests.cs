using Microsoft.Extensions.DependencyInjection;
using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Shouldly;
using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExpedientesAdministrativo;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.ServicosExternos.ServicosSei;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.ObjectMapping;
using Volo.Abp.Users;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ExpedienteAdministrativoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly IExpedienteAdministrativoAppService _service;
        private readonly IExpedienteAdministrativoRepository _repositoryMock;
        private readonly IExpedienteAdministrativoManager _managerMock;
        private readonly IBlocoSisprecRepository _blocoSisprecRepositoryMock;
        private readonly IServicoSei _servicoSeiMock;
        private readonly IObjectMapper _mapperMock;
        private readonly ICurrentUser _userMock;

        public ExpedienteAdministrativoAppServiceTests()
        {
            var services = new ServiceCollection();

            _repositoryMock = Substitute.For<IExpedienteAdministrativoRepository>();
            _blocoSisprecRepositoryMock = Substitute.For<IBlocoSisprecRepository>();
            _servicoSeiMock = Substitute.For<IServicoSei>();
            _managerMock = Substitute.For<IExpedienteAdministrativoManager>();
            _mapperMock = Substitute.For<IObjectMapper>();
            _userMock = Substitute.For<ICurrentUser>();


            services.AddSingleton(_repositoryMock);
            services.AddSingleton(_blocoSisprecRepositoryMock);
            services.AddSingleton(_servicoSeiMock);

            _mapperMock = ServiceProvider.GetRequiredService<IObjectMapper>();

            _service = new ExpedienteAdministrativoAppService(_repositoryMock, _managerMock, _mapperMock, _servicoSeiMock, _userMock);

            #region Insert ExpedienteAdministrativo

            _repositoryMock.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 1,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            _repositoryMock.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 2,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            _repositoryMock.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 3,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });


            _repositoryMock.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 4,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123456",
                NumeroExpedienteAdministrativo = null,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123456",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            #endregion

            #region Insert BlocoSisprec

            _blocoSisprecRepositoryMock.InsertAsync(new BlocoSisprec
            {
                BlocoSisprecId = 1,
                DataCriacao = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                StatusBloco = Enums.EStatusBloco.GERADO
            });

            _blocoSisprecRepositoryMock.InsertAsync(new BlocoSisprec
            {
                BlocoSisprecId = 2,
                DataCriacao = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                StatusBloco = Enums.EStatusBloco.GERADO
            });

            #endregion
        }


        [Fact]
        public async Task GetExpedientes_Deve_Retornar_Expedientes()
        {
            // Arrange
            var input = new ExpedienteAdministrativoGetListInput()
            {
                NumeroProcessoSei = "123",
                TipoExpedienteAdministrativo = null,
                DataExpedienteAdministrativo = null,
                NomeUsuario = null,
                ObservacaoExpedienteAdministrativo = null,
                StatusExpedienteAdminstrativo = null,
                BlocoSisprecId = null,
                NumeroRequisicao = null,
            };

            var expectedList = new List<ExpedienteAdministrativo>
            {
                new ExpedienteAdministrativo { NumeroProcessoSei = "123" }
            };

            //var expectedResult = new PagedResultDto<ExpedienteAdministrativoDto>
            //{
            //    TotalCount = expectedList.Count,
            //    Items = expectedList
            //};

            _repositoryMock.GetQueryableAsync().Returns(expectedList.BuildMock());

            // Act
            var result = await _service.GetExpedientes(input);

            // Assert
            result.Items.ShouldContain(x => x.NumeroProcessoSei == "123");
        }

        [Fact]
        public async Task GetExpedienteSemBloco_Deve_Retornar_Expedientes()
        {
            // Arrange
            var input = new ExpedienteAdministrativoSemBlocoGetListInput
            {
                NumeroProcessoSei = "123456"
            };

            var mockService = Substitute.For<IExpedienteAdministrativoAppService>();

            var listaEsperada = new List<ExpedienteAdministrativoSemBlocoDto>
            {
                new ExpedienteAdministrativoSemBlocoDto
                {
                    NumeroProcessoSei = "123456"
                }
            };

            mockService
                .GetExpedientesSemBloco(Arg.Is<ExpedienteAdministrativoSemBlocoGetListInput>(
                    i => i.NumeroProcessoSei == "123456"))
                .Returns(Task.FromResult(new PagedResultDto<ExpedienteAdministrativoSemBlocoDto>
                {
                    Items = listaEsperada,
                    TotalCount = listaEsperada.Count
                }));

            // Act
            var result = await mockService.GetExpedientesSemBloco(input);

            // Assert
            result.ShouldNotBeNull();
            result.Items.ShouldContain(x => x.NumeroProcessoSei == "123456");
        }

        [Fact]
        public async Task IncluirExpedienteEmBlocoExistente_Deve_Incluir_Corretamente()
        {
            //Arrange
            var blocoDto = new CreateSemBlocoDto
            {
                BlocoId = 1,
                ExpedienteId = new List<int> { 1, 2, 3 }
            };

            //Act
            var retorno = _service.IncluirExpedienteEmBlocoExistente(blocoDto);

            await retorno.ShouldNotThrowAsync();
        }

        public async Task GerarBloco_Deve_Gerar_Corretamente()
        {
            //Arrange
            var blocosDto = new List<SemBlocosDto>
            {
                new SemBlocosDto
                {
                    ExpedienteAdministrativoId = 1,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                },
                new SemBlocosDto
                {
                    ExpedienteAdministrativoId = 2,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                }
            };
            //Act
            await _service.GerarBloco(blocosDto);

            //Assert
            foreach (var bloco in blocosDto)
            {
                var expediente = await _repositoryMock.GetAsync(x => x.ExpedienteAdministrativoId == bloco.ExpedienteAdministrativoId);
                expediente.BlocoSisprecId.ShouldNotBeNull();
            }
        }

        [Fact]
        public async Task GerarBloco_Nao_Deve_Permitir_TipoExpedientes_Diferente()
        {
            // Arrange
            var blocosDto = new List<SemBlocosDto>
            {
                new SemBlocosDto
                {
                    ExpedienteAdministrativoId = 1,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                },
                new SemBlocosDto
                {
                    ExpedienteAdministrativoId = 2,
                    Tipo = Enums.ETipoExpedienteAdministrativo.CANCELAMENTO.ToString()
                }
            };

            var mockService = Substitute.For<IExpedienteAdministrativoAppService>();

            mockService.GerarBloco(Arg.Is<List<SemBlocosDto>>(x =>
                x.Count == 2 &&
                x[0].Tipo != x[1].Tipo
            )).Throws(new UserFriendlyException("Tipos de expediente diferentes não são permitidos."));

            // Act
            var exception = await Record.ExceptionAsync(() => mockService.GerarBloco(blocosDto));

            // Assert
            exception.ShouldBeOfType<UserFriendlyException>();
            exception.Message.ShouldBe("Tipos de expediente diferentes não são permitidos.");
        }
        [Fact]
        public async Task CreateAsync_Deve_Criar_Expediente_Valido_Com_Implementacao_Real()
        {
            _userMock.UserName.Returns("Usuario Teste");

            var input = new CreateUpdateExpedienteAdminstrativoDto
            {
                TipoExpedienteAdministrativo = ETipoExpedienteAdministrativo.ADITAMENTO.ToString(),
                ObservacaoExpedienteAdministrativo = "Motivo de Teste",
                Requisicoes = "[\"20240000041\"]",
                TipoProcessoSEI = ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO
            };

            var retornoSei = new ServicosExternos.ServicosSei.Model.RetornoGeracaoProcedimento() { IdProcedimento = "1234" };

            _servicoSeiMock.GerarProcedimentoAsync(Arg.Any<bool>(), Arg.Any<string>()).Returns(retornoSei);

            // Act
            var result = await _service.CreateAsync(input);

            // Assert
            result.ShouldNotBeNull();
            result.NomeUsuario.ShouldBe("Usuario Teste");
            result.StatusExpedienteAdminstrativo.ShouldBe(EStatusAdministrativo.GERADO.ToString());

        }
    }
}