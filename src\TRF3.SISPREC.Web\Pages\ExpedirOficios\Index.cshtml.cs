using Microsoft.AspNetCore.Mvc;
using TRF3.SISPREC.ExpedirOficios;
using TRF3.SISPREC.ExpedirOficios.Dtos;
using TRF3.SISPREC.Web.Pages.ExpedirOficios.ViewModels;
using Volo.Abp.ObjectMapping;

namespace TRF3.SISPREC.Web.Pages.ExpedirOficios
{
    public class IndexModel : SISPRECPageModel
    {
        private IExpedirOficioAppService _appService { get; set; }
        private readonly IObjectMapper _objectMapper;

        public IndexModel(IExpedirOficioAppService appService, IObjectMapper objectMapper)
        {
            _appService = appService;
            _objectMapper = objectMapper;
        }

        [BindProperty]
        public ExpedirOficioViewModel ViewModel { get; set; }

        public virtual async Task<IActionResult> OnPostEnviarEmailAsync()
        {
            var expedirOficioDto = _objectMapper.Map<ExpedirOficioViewModel, ExpedirOficioDto>(ViewModel);

            try
            {
                await _appService.EnviarEmail(expedirOficioDto);
                TempData["MensagemSucesso"] = "E-mail enviado com sucesso!";
            }
            catch (Exception ex)
            {
                TempData["MensagemErro"] = ex.Message;
            }

            return RedirectToPage();
        }
    }
}
