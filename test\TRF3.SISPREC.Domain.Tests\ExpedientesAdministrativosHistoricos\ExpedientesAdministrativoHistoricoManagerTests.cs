using NSubstitute;
using Shouldly;
using TRF3.SISPREC.ExpedientesAdministrativosHistorico;
using TRF3.SISPREC.ExpedientesAdministrativosHistorico.Servicos;
using TRF3.SISPREC.SincronizacaoLegado.Models.Ufep;

namespace TRF3.SISPREC.ExpedientesAdministrativosHistoricos
{
    public class ExpedientesAdministrativoHistoricoManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
    {
        #region privates
        private readonly ExpedientesAdministrativoHistoricoManager _expedienteAdministrativoHistoricoManager;
        private readonly IExpedienteAdministrativoHistoricoRepository _expedienteAdministrativoHistoricoRepository;

        #endregion
        public ExpedientesAdministrativoHistoricoManagerTests()
        {
            _expedienteAdministrativoHistoricoRepository = Substitute.For<IExpedienteAdministrativoHistoricoRepository>();
            _expedienteAdministrativoHistoricoManager = new ExpedientesAdministrativoHistoricoManager(_expedienteAdministrativoHistoricoRepository);
        }

        [Fact]
        public async void InserirImportacaoAsync_Deve_Retornar_Lista_ExpedienteAdmisnitrativoHistorico()
        {
            // Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativoSei();

            // Act  
            var retultado = _expedienteAdministrativoHistoricoManager.InserirImportacaoAsync("123456", 1, expedienteAdministrativo);

            // Assert
            retultado.ShouldNotBeNull();
            retultado.Count().ShouldBe(5);
        }


        #region CriaObjetos  
        private IEnumerable<ExpedienteSei> inicializaExpedienteAdministrativoSei()
        {
            return new List<ExpedienteSei>()
                {
                new ExpedienteSei {
                    num_protoc_requis = "123456",
                    id_proces_sei = "10000005655297",
                    num_proces_sei = "00350445320194038000",
                    num_expedi_admin = 1,
                    sta_expedi_admin = "C",
                    dat_geracao = new DateTime().Date.ToString(),
                    nom_login_geracao = "UsuarioGeracao",
                    dat_liberacao= new DateTime().Date.ToString(),
                    nom_login_liberacao = "UsuarioLiberacao",
                    dat_cumprimento= new DateTime().Date.ToString(),
                    nom_login_cumprimento = "UsuarioCumprimento"
                },
                new ExpedienteSei {
                    num_protoc_requis = "123456",
                    id_proces_sei = "10000005655297",
                    num_proces_sei = "00350445320194038000",
                    num_expedi_admin = 2,
                    sta_expedi_admin = "C",
                    dat_geracao = new DateTime().Date.ToString(),
                    nom_login_geracao = "UsuarioGeracao",
                    dat_liberacao= new DateTime().Date.ToString(),
                    nom_login_liberacao = "UsuarioLiberacao"
                },
                new ExpedienteSei {
                    num_protoc_requis = "123456",
                    id_proces_sei = "10000005655297",
                    num_proces_sei = "00350445320194038000",
                    num_expedi_admin = 3,
                    sta_expedi_admin = "C"
                }
            };
        }
        #endregion

    }
}
