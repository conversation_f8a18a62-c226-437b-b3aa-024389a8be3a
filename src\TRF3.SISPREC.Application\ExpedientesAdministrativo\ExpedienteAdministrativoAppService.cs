using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.Apoio;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.ProcessoSEI;
using TRF3.SISPREC.REquisicoesExpedientesAdministrativos;
using TRF3.SISPREC.ServicosExternos.ServicosSei;
using TRF3.SISPREC.ServicosExternos.ServicosSei.Model;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.ObjectMapping;
using Volo.Abp.Users;

namespace TRF3.SISPREC.ExpedientesAdministrativo
{
    public class ExpedienteAdministrativoAppService : BaseCrudAppService<ExpedienteAdministrativo,
                                                           ExpedienteAdministrativoDto,
                                                           int,
                                                           ExpedienteAdministrativoGetListInput,
                                                           CreateUpdateExpedienteAdminstrativoDto,
                                                           CreateUpdateExpedienteAdminstrativoDto>, IExpedienteAdministrativoAppService
    {
        #region Read-Only Fields
        private readonly IExpedienteAdministrativoManager _manager;
        private readonly IExpedienteAdministrativoRepository _repository;
        private readonly ICurrentUser _user;
        private readonly IObjectMapper _mapper;
        private readonly IServicoSei _seiServico;
        #endregion

        #region Constructors

        public ExpedienteAdministrativoAppService(IExpedienteAdministrativoRepository repository,
                                                  IExpedienteAdministrativoManager manager,
                                                  IObjectMapper objectMapper,
                                                  IServicoSei seiServico,
                                                  ICurrentUser user) : base(repository, manager)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _seiServico = seiServico;
            _user = user;
            _manager = manager;
            _mapper = objectMapper ?? throw new ArgumentNullException(nameof(objectMapper));
        }

        #endregion

        #region IExpedienteAdministrativoAppService Members

        public async Task<PagedResultDto<ExpedienteAdministrativoDto>> GetExpedientes(ExpedienteAdministrativoGetListInput input)
        {
            var query = (await _repository.GetQueryableAsync())
                            .Include(x => x.RequisicaoExpedienteAdministrativo)
                            .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                            .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                            .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario.Contains(input.NomeUsuario))
                            .WhereIf(input.StatusExpedienteAdminstrativo != null, x => x.StatusExpedienteAdminstrativo == input.StatusExpedienteAdminstrativo)
                            .WhereIf(input.BlocoSisprecId != null, x => x.BlocoSisprecId == input.BlocoSisprecId)
                            .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                            .Select(x => new ExpedienteAdministrativo()
                            {
                                NumeroProcessoSei = x.NumeroProcessoSei,
                                TipoExpedienteAdministrativo = x.TipoExpedienteAdministrativo,
                                DataExpedienteAdministrativo = x.DataExpedienteAdministrativo,
                                NomeUsuario = x.NomeUsuario,
                                ObservacaoExpedienteAdministrativo = x.ObservacaoExpedienteAdministrativo,
                                StatusExpedienteAdminstrativo = x.StatusExpedienteAdminstrativo,
                                BlocoSisprecId = x.BlocoSisprecId,
                                ExpedienteAdministrativoId = x.ExpedienteAdministrativoId
                            })
                            .Distinct()
                            .OrderByDescending(x => x.DataExpedienteAdministrativo)
                            .ThenByDescending(x => x.NumeroProcessoSei)
                            .AsQueryable();

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativo, IQueryable<ExpedienteAdministrativo>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            var dto = _mapper.Map<List<ExpedienteAdministrativo>, List<ExpedienteAdministrativoDto>>(query.ToList());
            return new PagedResultDto<ExpedienteAdministrativoDto>
            {
                Items = dto.ToList(),
                TotalCount = totalCount
            };
        }
        public async Task IncluirExpedienteEmBlocoExistente(CreateSemBlocoDto blocoDto)
        {
            foreach (var expedienteId in blocoDto.ExpedienteId)
                await _manager.IncluirExpedienteEmBlocoExistenteAsync(expedienteId, blocoDto.BlocoId);
        }
        public async Task GerarBloco(List<SemBlocosDto> blocosDto)
        {
            var tiposUnicos = blocosDto
                .Select(x => x.Tipo.ToLower())
                .Distinct()
                .ToList();

            if (tiposUnicos.Count > 1)
                throw new UserFriendlyException("Para geração do bloco, todos os expedientes devem ser do mesmo Tipo.");

            await _manager.GerarBlocoAsync(blocosDto.Select(x => x.ExpedienteAdministrativoId).ToList());
        }
        public async Task<PagedResultDto<ExpedienteAdministrativoSemBlocoDto>> GetExpedientesSemBloco(ExpedienteAdministrativoSemBlocoGetListInput input)
        {
            #region Query Expediente Administrativo

            var query = (await _repository.GetQueryableAsync())
                    .Include(x => x.RequisicaoExpedienteAdministrativo)
                .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario.Contains(input.NomeUsuario))
                .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                .Where(x => x.BlocoSisprecId == null && x.NumeroExpedienteAdministrativo == null)
                .Select(g => new ExpedienteAdministrativo
                {
                    NumeroProcessoSei = g.NumeroProcessoSei,
                    TipoExpedienteAdministrativo = g.TipoExpedienteAdministrativo,
                    DataExpedienteAdministrativo = g.DataExpedienteAdministrativo,
                    NomeUsuario = g.NomeUsuario,
                    ObservacaoExpedienteAdministrativo = g.ObservacaoExpedienteAdministrativo,
                    StatusExpedienteAdminstrativo = g.StatusExpedienteAdminstrativo,
                    BlocoSisprecId = g.BlocoSisprecId,
                    ExpedienteAdministrativoId = g.ExpedienteAdministrativoId
                })
                .Distinct()
                .AsQueryable();

            #endregion

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativo, IQueryable<ExpedienteAdministrativo>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            var dto = _mapper.Map<List<ExpedienteAdministrativo>, List<ExpedienteAdministrativoSemBlocoDto>>(query.ToList());
            return new PagedResultDto<ExpedienteAdministrativoSemBlocoDto>
            {
                Items = dto.ToList(),
                TotalCount = totalCount
            };
        }

        protected async override Task<ExpedienteAdministrativo> GetEntityByIdAsync(int id)
        {
            return await _repository.FindAsync(m => m.ExpedienteAdministrativoId == id) ?? new ExpedienteAdministrativo();
        }

        public override async Task<ExpedienteAdministrativoDto> CreateAsync(CreateUpdateExpedienteAdminstrativoDto input)
        {
            await CheckCreatePolicyAsync();

            input.NomeUsuario = _user.UserName!;
            input.StatusExpedienteAdminstrativo = EStatusAdministrativo.GERADO.ToString();

            var processoSei = await GerarProcessoSeiAsync();

            if (processoSei == null)
                throw new UserFriendlyException("Falha ao gerar processo SEI!");

            var expediente = CriarExpedienteAdministrativo(input, processoSei);

            await _manager.InserirAsync(expediente);

            return _mapper.Map<ExpedienteAdministrativo, ExpedienteAdministrativoDto>(expediente);

        }
        #endregion

        private async Task<RetornoGeracaoProcedimento?> GerarProcessoSeiAsync()
        {
            return await _seiServico.GerarProcedimentoAsync(true, ProcessoSeiConsts.ID_UNIDADE);
        }

        private ExpedienteAdministrativo CriarExpedienteAdministrativo(CreateUpdateExpedienteAdminstrativoDto input, RetornoGeracaoProcedimento processoSei)
        {
            var expediente = _mapper.Map<CreateUpdateExpedienteAdminstrativoDto, ExpedienteAdministrativo>(input);

            expediente.IdentificadorProcessoSei = processoSei.IdProcedimento;
            expediente.NumeroProcessoSei = processoSei.ProcedimentoFormatado!.OnlyNumbers();
            expediente.StatusExpedienteAdminstrativo = EStatusAdministrativo.GERADO;
            expediente.TipoExpedienteAdministrativo = (ETipoExpedienteAdministrativo)Enum.Parse(
                                                        typeof(ETipoExpedienteAdministrativo),
                                                        input.TipoExpedienteAdministrativo ?? ""
                                                    );

            expediente.ExpedienteAdministrativoHistoricos =
            [
                new()
                {
                    StatusAdministrativo = EStatusAdministrativo.GERADO,
                    NomeUsuario = input.NomeUsuario!,
                    DataStatus = input.DataExpedienteAdministrativo!.Value
                }
            ];

            if (!string.IsNullOrWhiteSpace(input.RequisicoesFormat))
            {
                expediente.RequisicaoExpedienteAdministrativo = input.RequisicoesFormat
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(i => new RequisicaoExpedienteAdministrativo { NumeroProtocoloRequisicao = i.Trim() })
                    .ToList();
            }

            return expediente;
        }
    }
}
