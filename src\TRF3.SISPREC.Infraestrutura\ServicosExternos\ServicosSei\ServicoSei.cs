using Microsoft.Extensions.Configuration;
using TRF3.SISPREC.Enums;
using ServicosSei;
using AutoMapper;
using System.Diagnostics.CodeAnalysis;
using RetornoGeracaoProcedimento = ServicosSei.RetornoGeracaoProcedimento;
using RetornoInclusaoDocumento = ServicosSei.RetornoInclusaoDocumento;
using RetornoEnvioEmail = ServicosSei.RetornoEnvioEmail;

namespace TRF3.SISPREC.ServicosExternos.ServicosSei
{
    public sealed class ServicoSei : IServicoSei
    {
        #region Read-Only Fields

        private readonly SeiPortType _seiSoapClient;
        private readonly IConfiguration _configuration;
        private string _chaveSEI;
        private string _nomeSistema;

        #endregion

        #region Constructors

        public ServicoSei(SeiPortType seiSoapClient, IConfiguration configuration)
        {
            _seiSoapClient = seiSoapClient ?? throw new ArgumentNullException(nameof(seiSoapClient));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _chaveSEI = _configuration["ConsultaSeiService:Chave"] ?? throw new ArgumentNullException(nameof(configuration));
            _nomeSistema = _configuration["ConsultaSeiService:Servico"] ?? throw new ArgumentNullException(nameof(configuration));
        }

        #endregion

        #region IConsultaSeiService Members

        public async Task<Model.RetornoGeracaoProcedimento> GerarProcedimentoAsync(bool isNormal, string idUnidade, EConsultaSeiNivelAcesso? nivelAcesso = EConsultaSeiNivelAcesso.PUBLICO)
        {
            string idEsto = isNormal ? "100000166" : "100000757";
            var processo = new Procedimento
            {
                IdTipoProcedimento = idEsto,
                Especificacao = "",
                Assuntos = new Assunto[0] { },
                Interessados = new Interessado[0] { },
                Observacao = "",
                NivelAcesso = nivelAcesso?.GetEnumDescription()
            };
            var documentos = new Documento[] { };
            var procedimento = new string[0] { };
            var unidade = new string[0] { };

            var result = await _seiSoapClient.gerarProcedimentoAsync(
                                                             _nomeSistema,
                                                             _chaveSEI,
                                                             idUnidade,
                                                             processo,
                                                             documentos,
                                                             procedimento,
                                                             unidade,
                                                             "S", "N", "", "", "", "", "", "", "", ""
            );

            var config = new MapperConfiguration(cfg =>
            {
                // Mapeamento do objeto principal
                cfg.CreateMap<RetornoGeracaoProcedimento, Model.RetornoGeracaoProcedimento>();

                // Mapeamento do array de documentos
                cfg.CreateMap<RetornoInclusaoDocumento, Model.RetornoInclusaoDocumento>();
            });

            var mapper = config.CreateMapper();
            var retornoGeracaoProcedimento = mapper.Map<Model.RetornoGeracaoProcedimento>(result);
            return retornoGeracaoProcedimento;
        }

        [ExcludeFromCodeCoverage]
        public async Task<Model.RetornoEnvioEmail> EnviarEmail(string de, string para, string cco, string assunto, string mensagem, string[] idDocumentos, string idHipoteseLegal)
        {

            var result = await _seiSoapClient.enviarEmailAsync(
                _nomeSistema,
                _chaveSEI,
                "110002835",
                "0004080-67.2025.4.03.8000",
                de,
                para,
                cco,
                assunto,
                mensagem,
                idDocumentos,
                EConsultaSeiNivelAcesso.PUBLICO.ToString(),
                idHipoteseLegal
                );

            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<RetornoEnvioEmail, Model.RetornoEnvioEmail>();
            });

            var mapper = config.CreateMapper();
            var retornoEnvioEmail = mapper.Map<Model.RetornoEnvioEmail>(result);

            return retornoEnvioEmail;

        }
        #endregion
    }
}
