using System.ComponentModel.DataAnnotations;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ExpedirOficios.ViewModels
{
    public class ExpedirOficioViewModel
    {
        [Display(Name = "De:")]
        [Required]
        public string Remetente { get; set; } = string.Empty;

        [Display(Name = "Enviar com cópia oculta")]
        public bool EnviarCopia { get; set; } = false;

        [Display(Name = "Para:")]
        [Required]
        public string Destinatarios { get; set; } = string.Empty;

        [Display(Name = "Assunto:")]
        [Required]
        public string Assunto { get; set; } = string.Empty;

        [Display(Name = "Mensagem:")]
        [TextArea]
        [Required]
        public string Mensagem { get; set; } = string.Empty;

    }
}
