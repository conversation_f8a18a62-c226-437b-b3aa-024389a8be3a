using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Shouldly;
using TRF3.SISPREC.ExpedirOficios;
using TRF3.SISPREC.ExpedirOficios.Dtos;
using TRF3.SISPREC.ServicosExternos.ServicosSei;
using TRF3.SISPREC.ServicosExternos.ServicosSei.Model;
using Volo.Abp;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ExpedirOficioAppServiceTests
    {
        private readonly IExpedirOficioAppService _service;
        private readonly IServicoSei _servicoSeiMock;

        public ExpedirOficioAppServiceTests()
        {
            var services = new ServiceCollection();

            _servicoSeiMock = Substitute.For<IServicoSei>();

            var retornoMock = new RetornoEnvioEmail();
            _servicoSeiMock.EnviarEmail(
                Arg.Any<string>(),
                Arg.Any<string>(),
                Arg.Any<string>(),
                Arg.Any<string>(),
                Arg.Any<string>(),
                Arg.Any<string[]>(),
                Arg.Any<string>())
                .Returns(Task.FromResult(retornoMock));



            services.AddSingleton<IServicoSei>(_servicoSeiMock);

            services.AddTransient<IExpedirOficioAppService, ExpedirOficioAppService>();

            var provider = services.BuildServiceProvider();

            _service = provider.GetRequiredService<IExpedirOficioAppService>();
        }

        [Fact]
        public async Task EnviarEmail_Deve_Chamar_IServicoSei_Com_Parametros_Corretos()
        {
            var dto = new ExpedirOficioDto
            {
                Remetente = "<EMAIL>",
                Destinatarios = "<EMAIL>",
                Assunto = "Assunto Teste",
                Mensagem = "Mensagem de Teste",
                EnviarCopia = false
            };

            var retornoMock = new RetornoEnvioEmail
            {
                DocumentoFormatado = "test",
                IdDocumento = "test",
                LinkAcesso = "test"
            };

            _servicoSeiMock.EnviarEmail(
                dto.Remetente,
                dto.Destinatarios,
                dto.Destinatarios,
                dto.Assunto,
                dto.Mensagem,
                null,
                string.Empty
            ).Returns(Task.FromResult(retornoMock));

            var result1 = await _service.EnviarEmail(dto);

            result1.ShouldNotBeNull();
        }

        [Fact]
        public async Task EnviarEmail_QuandoServicoSeiLancaExcecao_DeveLancarExceptionComMensagemFormatada()
        {

            var dto = new ExpedirOficioDto
            {
                Remetente = "<EMAIL>",
                Destinatarios = "<EMAIL>",
                Assunto = "Erro Teste",
                Mensagem = "Deve falhar",
                EnviarCopia = false
            };

            var mensagemErroOriginal = "Erro de conexão";

            _servicoSeiMock
                .EnviarEmail(
                    Arg.Any<string>(),
                    Arg.Any<string>(),
                    Arg.Any<string>(),
                    Arg.Any<string>(),
                    Arg.Any<string>(),
                    Arg.Any<string[]>(),
                    Arg.Any<string>())
                .Throws(new UserFriendlyException(mensagemErroOriginal));

            var mensagemEsperada = $"Falha na comunicação com o sistema SEI. Mensagem: {mensagemErroOriginal}";

            var ex = await Assert.ThrowsAsync<UserFriendlyException>(() => _service.EnviarEmail(dto));

            ex.Message.ShouldBe(mensagemEsperada);
        }

        [Fact]
        public async Task EnviarEmail_QuandoEnviarCopiaForTrue_DeveIncluirDestinatariosEmCopia()
        {
            var destinatarios = " <EMAIL> ";
            var destinatariosEsperado = destinatarios.Trim();

            var dto = new ExpedirOficioDto
            {
                Remetente = "<EMAIL>",
                Destinatarios = destinatarios,
                Assunto = "Assunto com cópia",
                Mensagem = "Mensagem de Teste",
                EnviarCopia = true
            };

            var retornoMock = new RetornoEnvioEmail
            {
                DocumentoFormatado = "test",
                IdDocumento = "test",
                LinkAcesso = "test"
            };

            _servicoSeiMock.EnviarEmail(
                dto.Remetente,
                dto.Destinatarios,
                destinatariosEsperado,
                dto.Assunto,
                dto.Mensagem,
                null,
                string.Empty
            ).Returns(Task.FromResult(retornoMock));

            var result = await _service.EnviarEmail(dto);

            result.ShouldNotBeNull();

            await _servicoSeiMock.Received(1).EnviarEmail(
                dto.Remetente,
                dto.Destinatarios,
                destinatariosEsperado,
                dto.Assunto,
                dto.Mensagem,
                null,
                string.Empty
            );
        }


    }
}
