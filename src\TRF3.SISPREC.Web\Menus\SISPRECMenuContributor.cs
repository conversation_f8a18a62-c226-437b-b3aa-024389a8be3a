using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Permissoes;
using Volo.Abp.UI.Navigation;
using Volo.Abp.Users;

namespace TRF3.SISPREC.Web.Menus;

[ExcludeFromCodeCoverage]
public class SISPRECMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        ICurrentUser currentUser = context.ServiceProvider.GetRequiredService<ICurrentUser>();

        if (currentUser.IsAuthenticated)
        {
            ConfigurarMenuCadastros(context);

            ConfigurarMenuRequisicoes(context);

            ConfigurarMenuAnalises(context);

            ConfigurarMenuExpedienteAdministrativo(context);

            ConfigurarMenuGerencial(context);

            ConfigurarMenuImportacao(context);

            ConfigurarMenuTransmissaoCjf(context);

            ConfigurarMenuConfiguracoesAplicacao(context);

        }

        return Task.CompletedTask;
    }

    private static void ConfigurarMenuTransmissaoCjf(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(7,
                    new ApplicationMenuItem(
                        SISPRECMenus.TransmissaoCJF,
                        "Transmissão para o CJF",
                        null,
                        icon: "fa-solid fa-paper-plane",
                        order: 8,
                        requiredPermissionName: SISPRECPermissoes.TransmissaoCJF.Visualizar
                    ));

        // Obtém o item de menu "Transmissão para CJF"
        var transmissaoCjfItemMenu = context.Menu.GetMenuItem(SISPRECMenus.TransmissaoCJF);

        #region Menu "Cadastros Básicos CJF"

        // Adiciona o subitem "Cadastros" ao menu "Cadastros Básicos"
        transmissaoCjfItemMenu.Items.Insert(0,
                          new ApplicationMenuItem(
                            SISPRECMenus.CadastrosBasicosCJF,
                            "Cadastros",
                            null,
                            icon: "fa-solid fa-table-list"
                        ));

        // Adiciona o subitem "Sincronização" ao menu "Cadastros Básicos"
        transmissaoCjfItemMenu.Items.Insert(1,
        new ApplicationMenuItem(
                 SISPRECMenus.SincronizacaoCJF,
                 "Sincronização",
                 "/SincronizacoesDominios",
                 icon: "fa-solid fa-rotate"
        ));


        // Obtém o item de menu "Cadastros"
        var entidadesDominioItemMenu = transmissaoCjfItemMenu.GetMenuItem(SISPRECMenus.CadastrosBasicosCJF);

        List<ApplicationMenuItem> applicationMenuItems =
        [
            new ApplicationMenuItem(SISPRECMenus.Assunto, "Assunto", "/Assuntos", requiredPermissionName: SISPRECPermissoes.Assunto.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.BeneficiarioTipo, "Beneficiário - Tipo", "/BeneficiarioTipos", requiredPermissionName: SISPRECPermissoes.BeneficiarioIdentificacaoTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.BeneficiarioSucessaoTipo, "Beneficiário - Tipo Sucessão", "/BeneficiarioSucessaoTipos", requiredPermissionName: SISPRECPermissoes.BeneficiarioSucessaoTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.BeneficiarioIdentificacaoTipo, "Beneficiário - Tipo Identificação", "/BeneficiarioIdentificacaoTipos", requiredPermissionName: SISPRECPermissoes.BeneficiarioTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.DespesaClassificacao, "Despesa - Classificação", "/DespesaClassificacoes", requiredPermissionName: SISPRECPermissoes.DespesaClassificacao.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.DespesaNatureza, "Despesa - Natureza", "/DespesaNaturezas", requiredPermissionName: SISPRECPermissoes.DespesaNatureza.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.DespesaTipo, "Despesa - Tipo", "/DespesaTipos", requiredPermissionName: SISPRECPermissoes.DespesaTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.IndiceAtualizacaoMonetaria, "Atualiz. Monetária - Índice", "/IndiceAtualizacaoMonetarias", requiredPermissionName: SISPRECPermissoes.IndiceAtualizacaoMonetaria.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.IndiceAtualizacaoMonetariaTipo, "Atualiz. Monetária - Tipo", "/IndiceAtualizacaoMonetariaTipos", requiredPermissionName: SISPRECPermissoes.IndiceAtualizacaoMonetariaTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.FaseTipo, "Fase - Tipo", "/FaseTipos", requiredPermissionName: SISPRECPermissoes.FaseTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.MovimentoTipo, "Movimento - Tipo", "/MovimentoTipos", requiredPermissionName: SISPRECPermissoes.MovimentoTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.OrdemPagamento107aTipo, "Ordem de Pagamento 107", "/OrdemPagamento107aTipos", requiredPermissionName: SISPRECPermissoes.OrdemPagamento107aTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.SentencaTipo, "Sentença - Tipo", "/SentencaTipos"),
                new ApplicationMenuItem(SISPRECMenus.ServidorCondicaoTipo, "Servidor - Tipo Condição", "/ServidorCondicaoTipos", requiredPermissionName: SISPRECPermissoes.ServidorCondicaoTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadeJudicial, "Unidade Judicial", "/UnidadesJudiciais", requiredPermissionName: SISPRECPermissoes.UnidadeJudicial.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadeJudicialTipo, "Unidade Judicial - Tipo", "/UnidadeJudicialTipos", requiredPermissionName: SISPRECPermissoes.UnidadeJudicialTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadeJudicialTipoNatureza, "Unidade Judicial - Natureza", "/UnidadeJudicialTipoNaturezas", requiredPermissionName: SISPRECPermissoes.UnidadeJudicialTipoNatureza.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadeOrcamentaria, "Unidade Orçamentária", "/UnidadesOrcamentarias", requiredPermissionName: SISPRECPermissoes.UnidadeOrcamentaria.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadeGestora, "Unidade Gestora", "/UnidadesGestoras", requiredPermissionName: SISPRECPermissoes.UnidadeGestora.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.ValorTipo, "Valor - Tipo", "/ValorTipos", requiredPermissionName: SISPRECPermissoes.ValorTipo.Visualizar),
            ];

        // Ordena os itens
        var sortedItems = applicationMenuItems.OrderBy(x => x.DisplayName).ToList();

        // Adiciona os subitens ao menu "Cadastros"
        foreach (var item in sortedItems)
        {
            entidadesDominioItemMenu.AddItem(item);
        }

        #endregion


        // Adiciona os subitens ao menu "Transmissão para CJF"
        transmissaoCjfItemMenu.Items.Insert(2,
        new ApplicationMenuItem(
            SISPRECMenus.Fases,
            "Fases",
            "/ViewFases",
            icon: "fa-solid fa-folder-closed",
            requiredPermissionName: SISPRECPermissoes.TransmissaoCJF.Visualizar
        ));

        transmissaoCjfItemMenu.Items.Insert(3,
            new ApplicationMenuItem(
                 SISPRECMenus.Controles,
                 "Controles",
                 "/ViewControles",
                icon: "fa-solid fa-sliders",
                requiredPermissionName: SISPRECPermissoes.TransmissaoCJF.Visualizar
            ));

        transmissaoCjfItemMenu.Items.Insert(4,
            new ApplicationMenuItem(
                 SISPRECMenus.Processamento,
                 "Processamento",
                 "/ViewControles/Processamento",
                icon: "fa-solid fa-bars-progress",
                requiredPermissionName: SISPRECPermissoes.TransmissaoCJF.Visualizar
            ));

        transmissaoCjfItemMenu.Items.Insert(5,
            new ApplicationMenuItem(
                SISPRECMenus.ServicosTransmissaoCJF,
                "Serviços",
                "/ProcessaPrecatorio/ConfiguracaoServico",
                icon: "fa-solid fa-power-off",
                requiredPermissionName: SISPRECPermissoes.TransmissaoCJF.Visualizar
            ));
    }

    private static void ConfigurarMenuCadastros(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(0,
            new ApplicationMenuItem(
                SISPRECMenus.Cadastros,
                "Cadastros",
                null,
                icon: "fa-solid fa-table-list",
                order: 1,
                requiredPermissionName: SISPRECPermissoes.RequisicaoProtocolo.Visualizar
            )
        );

        // Obtém o item de menu "Cadastros"
        var cadastrosMenu = context.Menu.GetMenuItem(SISPRECMenus.Cadastros);

        List<ApplicationMenuItem> applicationRequisitoriosMenuItems =
        [
            new ApplicationMenuItem(SISPRECMenus.AdvogadosJudiciais, "Advogado Judicial", "/AdvogadosJudiciais", requiredPermissionName: SISPRECPermissoes.AdvogadoJudicial.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Pessoas, "Pessoa", "/Pessoas", requiredPermissionName: SISPRECPermissoes.Pessoa.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Setor, "Setor", "/Setor", requiredPermissionName: SISPRECPermissoes.Setor.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.SituacaoRequisicao, "Situação de Requisição", "/SituacoesRequisicoesProtocolos", requiredPermissionName: SISPRECPermissoes.Perfil.AdminTI),
                new ApplicationMenuItem(SISPRECMenus.TiposProcedimentos, "Tipo de Procedimento", "/TiposProcedimentos", requiredPermissionName: SISPRECPermissoes.Perfil.AdminTI),
                new ApplicationMenuItem(SISPRECMenus.AcaoJustificativa, "Motivo Justificativa", "/AcoesJustificativa", requiredPermissionName: SISPRECPermissoes.AcaoJustificativa.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.MotivoOcorrencia, "Motivo Ocorrência", "/OcorrenciaMotivos", requiredPermissionName: SISPRECPermissoes.RequisicaoOcorrencia.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.MotivoExpedienteAdministrativo, "Motivo Expediente Administrativo", "/MotivosExpedientesAdministrativos", requiredPermissionName: SISPRECPermissoes.MotivoExpedienteAdministrativo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.ModeloDocumento, "Modelo de Documento", "/ModelosDocumentos", requiredPermissionName: SISPRECPermissoes.ModeloDocumento.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.UnidadesEquivalentes, "Unidades Equivalentes", "/UnidadesEquivalentes", requiredPermissionName: SISPRECPermissoes.UnidadeEquivalente.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.IndicadorEconomicoTipo, "Indicador Econômico - Tipo", "/IndicadorEconomicoTipos", requiredPermissionName: SISPRECPermissoes.IndicadorEconomicoTipo.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.IndicadorEconomico, "Indicador Econômico - Valores", "/IndicadorEconomicos", requiredPermissionName: SISPRECPermissoes.IndicadorEconomico.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Agencia, "Agência", "/Agencias", requiredPermissionName: SISPRECPermissoes.Agencia.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Banco, "Banco", "/Bancos", requiredPermissionName: SISPRECPermissoes.Banco.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Perito, "Perito Autorizado", "/Peritos", requiredPermissionName: SISPRECPermissoes.Perito.Visualizar),
                new ApplicationMenuItem(SISPRECMenus.Propostas, "Propostas", "/Propostas", requiredPermissionName: SISPRECPermissoes.Proposta.Visualizar)
        ];

        // Ordena os itens
        var sortedRequsitoriosItems = applicationRequisitoriosMenuItems.OrderBy(x => x.DisplayName).ToList();

        // Adiciona os subitens ao menu "Cadastros"
        foreach (var item in sortedRequsitoriosItems)
        {
            cadastrosMenu.AddItem(item);
        }
    }

    private static void ConfigurarMenuRequisicoes(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(1,
            new ApplicationMenuItem(
                SISPRECMenus.Requisicoes,
                "Requisições",
                null,
                icon: "fa-solid fa-file-alt",
                order: 2,
                requiredPermissionName: SISPRECPermissoes.RequisicaoProtocolo.Visualizar
            )
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ViewRequisicao, "Listagem", "/ViewRequisicoes"))
            .AddItem(new ApplicationMenuItem(SISPRECMenus.RelatorioPorJuizo, "Relatório por Juízo", "/RelatorioPorJuizos"))
        );
    }

    private static void ConfigurarMenuAnalises(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(2,
            new ApplicationMenuItem(
                SISPRECMenus.Analises,
                "Análises",
                null,
                icon: "fa-solid fa-magnifying-glass-chart",
                order: 3,
                requiredPermissionName: SISPRECPermissoes.MenuAnalises.Visualizar
             )
                .AddItem(new ApplicationMenuItem(SISPRECMenus.Pendencias, "CPF/CNPJ", "/AnaliseCpfCnpj"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.AnalisePrevencao, "Prevenção", "/AnalisePrevencoes"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.AnaliseReinclusao, "Reinclusão", "/AnaliseReinclusao"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.Pendencias, "Pendências", "/AnalisePendencias", cssClass: "analise-sub-menu-borda-bottom"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.ConferirObservacao, "Campo Observação", "/ConferirObservacao"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.ConferirOrgaoPss, "Órgão PSS", "/ConferirOrgaoPss"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.AnaliseNomesPartes, "Nomes de Partes", "/AnaliseNomesPartes", cssClass: "analise-sub-menu-borda-bottom"))
                .AddItem(new ApplicationMenuItem(SISPRECMenus.ConsultarJustificativa, "Consultar Justificativa", "/ConsultarJustificativa"))
        );
    }

    private static void ConfigurarMenuExpedienteAdministrativo(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(3,
            new ApplicationMenuItem(
                SISPRECMenus.ExpedienteAdministrativo,
                "Expediente Administrativo",
                null,
                icon: "fa-solid fa-file-signature",
                order: 4,
                requiredPermissionName: SISPRECPermissoes.MenuExpedienteAdministrativo.Visualizar
            )
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ExpedienteAdministrativoListagem, "Listagem", "/ExpedientesAdministrativos"))
            .AddItem(new ApplicationMenuItem(SISPRECMenus.BlocosListagem, "Blocos", "/Blocos"))
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ExpedienteAdministrativoSemBloco, "Sem Bloco", "/ExpedientesAdministrativos/SemBloco"))
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ExpedirOficio, "Expedir Ofício", "/ExpedirOficios"))
        );
    }

    private static void ConfigurarMenuGerencial(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(4,
            new ApplicationMenuItem(
                SISPRECMenus.Gerencial,
                "Gerencial",
                null,
                icon: "bi bi-clipboard-check-fill",
                order: 5,
                requiredPermissionName: SISPRECPermissoes.MenuGerencial.Visualizar
            )
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ConfirmarLiberacao, "Confirmar Liberação", "/ConfirmarLiberacao"))
        );
    }

    // private static void ConfigurarMenuPropostas(MenuConfigurationContext context)
    // {
    //     context.Menu.Items.Insert(5,
    //         new ApplicationMenuItem(
    //             SISPRECMenus.Propostas,
    //             "/Propostas",
    //             null,
    //             icon: "fa-solid fa-folder",
    //             order: 6,
    //             requiredPermissionName: SISPRECPermissoes.Proposta.Visualizar
    //         )
    //         .AddItem(new ApplicationMenuItem(SISPRECMenus.Propostas, "Propostas", "/Propostas"))
    //     );
    // }

    private static void ConfigurarMenuImportacao(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(6,
            new ApplicationMenuItem(
                SISPRECMenus.Requisicoes,
                "Importação",
                null,
                icon: "fa fa-file-arrow-up",
                order: 7,
                requiredPermissionName: SISPRECPermissoes.MenuImportacao.Visualizar
            )
            .AddItem(new ApplicationMenuItem(SISPRECMenus.ViewRequisicao, "Listagem de Erros", "/ViewErrosImportacao"))
        );
    }

    private static void ConfigurarMenuConfiguracoesAplicacao(MenuConfigurationContext context)
    {
        context.Menu.Items.Insert(8,
            new ApplicationMenuItem(
                SISPRECMenus.Settings,
                "Configurações da Aplicação",
                null,
                icon: "fa-solid fa-gear",
                order: 9,
                requiredPermissionName: SISPRECPermissoes.ConfiguracoesAplicacao.Gravar
            ));

        var menuConfiguracoes = context.Menu.GetMenuItem(SISPRECMenus.Settings);
        menuConfiguracoes.AddItem(new ApplicationMenuItem(SISPRECMenus.ImportacoesSettings, "Importações", "/Settings/Importacoes"));
        menuConfiguracoes.AddItem(new ApplicationMenuItem(SISPRECMenus.VerificacaoRequisicoesSettings, "Verificação de Requisições", "/Settings/RequisicoesVerificacoes"));

        var menuAdm = context.Menu.GetAdministration();
        context.Menu.Items.Remove(menuAdm);
    }
}
