using TRF3.SISPREC.Domain;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.SincronizacaoLegado.Models.Ufep;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ExpedientesAdministrativosHistorico.Servicos
{
    [ExposeServices(typeof(IExpedienteAdministrativoHistoricoManager))]
    public class ExpedientesAdministrativoHistoricoManager : BaseDomainManager<ExpedienteAdministrativoHistorico>, IExpedienteAdministrativoHistoricoManager
    {
        public ExpedientesAdministrativoHistoricoManager(IRepository<ExpedienteAdministrativoHistorico> repository) : base(repository)
        {
        }

        public IEnumerable<ExpedienteAdministrativoHistorico> InserirImportacaoAsync(string numeroRequisicao, int numeroExpedienteAdministrativo, IEnumerable<ExpedienteSei> expedienteAdministrativoSEI)
        {
            var listaExpedienteAdministrativoHistorico = new List<ExpedienteAdministrativoHistorico>();
            foreach (var expedienteSei in expedienteAdministrativoSEI)
            {
                var expedienteId = expedienteSei.num_expedi_admin;

                var campos = new[]
                {
                    new { Data = expedienteSei.dat_geracao, Usuario = expedienteSei.nom_login_geracao, Status = EStatusAdministrativo.GERADO},
                    new { Data = expedienteSei.dat_liberacao, Usuario = expedienteSei.nom_login_liberacao, Status = EStatusAdministrativo.LIBERADO_CUMPRIMENTO},
                    new { Data = expedienteSei.dat_cumprimento, Usuario = expedienteSei.nom_login_cumprimento, Status = EStatusAdministrativo.CUMPRIDO },
                    new { Data = expedienteSei.dat_expedicao, Usuario = expedienteSei.nom_login_expedicao, Status = EStatusAdministrativo.EXPEDIDO },
                };

                foreach (var campo in campos)
                {
                    if (campo.Data != null)
                    {
                        var historico = new ExpedienteAdministrativoHistorico
                        {
                            ExpedienteAdministrativoId = expedienteId,
                            DataStatus = DateTime.Parse(campo.Data.ToString()),
                            NomeUsuario = campo.Usuario,
                            StatusAdministrativo = campo.Status
                        };

                        listaExpedienteAdministrativoHistorico.Add(historico);
                    }
                }

            }
            return listaExpedienteAdministrativoHistorico;
        }
    }
}
