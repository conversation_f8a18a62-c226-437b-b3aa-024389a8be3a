using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ServicosExternos.ServicosSei.Model;
using Volo.Abp.DependencyInjection;

namespace TRF3.SISPREC.ServicosExternos.ServicosSei
{
    public interface IServicoSei : ITransientDependency
    {
        Task<RetornoGeracaoProcedimento> GerarProcedimentoAsync(bool isNormal, string idUnidade, EConsultaSeiNivelAcesso? nivelAcesso = EConsultaSeiNivelAcesso.PUBLICO);
        Task<RetornoEnvioEmail> EnviarEmail(string de, string para, string cco, string assunto, string mensagem, string[] idDocumentos, string idHipoteseLegal);
    }
}
